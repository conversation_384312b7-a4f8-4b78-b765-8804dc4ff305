# 💕 爱不爱我测试程序 - 使用指南

## 🎯 项目简介

这是一个有趣的Python GUI小程序，专门用来"测试"女朋友是否爱你！程序包含两个版本：

- **main.py** - 基础版本，简单易用
- **advanced_version.py** - 高级版本，包含动画和更多互动功能

## 🚀 快速开始

### 方法一：双击运行（推荐）
1. 双击 `run.bat` 文件
2. 程序会自动启动

### 方法二：命令行运行
```bash
# 运行基础版本
python main.py

# 运行高级版本
python advanced_version.py
```

## 🎮 程序功能

### 基础版本功能：
- ✨ 粉色可爱界面
- 💖 两个选择按钮："爱我" 和 "不爱我"
- 🎯 点击"不爱我"时按钮会随机移动
- 💬 多种有趣的反问消息
- 🚫 阻止直接关闭程序
- ✅ 只有选择"爱我"才能正常关闭

### 高级版本额外功能：
- 💓 心跳动画效果
- 📊 拒绝次数统计
- 🎨 根据拒绝次数改变界面颜色
- 🔄 按钮移动动画
- 📏 按钮会越来越小越来越难点击
- 💖 最终爱心绘制界面

## 🎪 使用场景

### 情侣互动：
- 约会时的小游戏
- 增进感情的互动工具
- 制造浪漫氛围

### 表白神器：
- 向心仪对象表白时使用
- 创造有趣的互动体验
- 增加表白成功率

### 朋友聚会：
- 聚会时的娱乐项目
- 活跃气氛的小工具
- 制造欢乐时光

## 🛠️ 自定义修改

### 修改反问消息：
在 `main.py` 中找到 `rejection_messages` 列表，添加你想要的消息：

```python
self.rejection_messages = [
    "你自己的消息1 😢",
    "你自己的消息2 💭",
    # 添加更多消息...
]
```

### 修改界面颜色：
```python
# 修改背景颜色
self.root.configure(bg='#你的颜色代码')

# 修改按钮颜色
bg='#你的颜色代码',
fg='#文字颜色代码'
```

### 修改窗口大小：
```python
self.root.geometry("宽度x高度")  # 例如："800x600"
```

## 📱 分享给女朋友

### 方法一：发送文件
1. 将整个文件夹打包成zip
2. 发送给女朋友
3. 告诉她双击 `run.bat` 运行

### 方法二：制作快捷方式
1. 右键点击 `run.bat`
2. 选择"创建快捷方式"
3. 重命名为"💕 点我测试爱不爱你 💕"
4. 发送快捷方式给女朋友

### 方法三：打包成exe（高级）
```bash
# 安装打包工具
pip install pyinstaller

# 打包基础版本
pyinstaller --onefile --windowed main.py

# 打包高级版本
pyinstaller --onefile --windowed advanced_version.py
```

## ⚠️ 注意事项

1. **程序特性**：
   - 程序会阻止直接关闭
   - 只有选择"爱我"才能正常退出
   - 如果卡死可通过任务管理器结束

2. **系统要求**：
   - Python 3.6+ 版本
   - Windows/macOS/Linux 系统
   - 支持tkinter（通常已内置）

3. **使用建议**：
   - 这只是娱乐程序，请理性使用
   - 不要在重要场合使用，避免尴尬
   - 确保对方有幽默感再使用

## 🎁 扩展想法

### 可以添加的功能：
- 🎵 背景音乐播放
- 📸 截图保存功能
- 💌 自定义表白文字
- 🎨 更多主题颜色选择
- 📊 统计数据保存
- 🔊 语音播报功能

### 其他创意用法：
- 制作生日祝福版本
- 创建节日问候版本
- 设计求婚专用版本
- 制作道歉专用版本

## 💝 温馨提示

记住，真正的爱情不需要程序来测试，这只是一个增进感情的小工具。最重要的是真诚的沟通和相互理解！

---

💕 祝你们幸福美满！ 💕
