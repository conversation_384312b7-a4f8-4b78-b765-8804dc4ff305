#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
爱不爱我测试程序 - 启动器
让用户选择运行哪个版本
"""

import tkinter as tk
from tkinter import messagebox
import subprocess
import sys
import os

class LauncherApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("💕 爱不爱我测试程序启动器 💕")
        self.root.geometry("400x300")
        self.root.configure(bg='#FFE4E1')
        self.root.resizable(False, False)
        
        # 居中显示窗口
        self.center_window()
        
        self.setup_ui()
        
    def center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
    def setup_ui(self):
        """设置用户界面"""
        # 标题
        title_label = tk.Label(
            self.root,
            text="💕 爱不爱我测试程序 💕",
            font=("微软雅黑", 20, "bold"),
            bg='#FFE4E1',
            fg='#FF1493'
        )
        title_label.pack(pady=30)
        
        # 副标题
        subtitle_label = tk.Label(
            self.root,
            text="请选择要运行的版本",
            font=("微软雅黑", 14),
            bg='#FFE4E1',
            fg='#FF69B4'
        )
        subtitle_label.pack(pady=10)
        
        # 按钮框架
        button_frame = tk.Frame(self.root, bg='#FFE4E1')
        button_frame.pack(pady=30)
        
        # 基础版本按钮
        basic_button = tk.Button(
            button_frame,
            text="🌸 基础版本 🌸\n简单易用",
            font=("微软雅黑", 14, "bold"),
            bg='#FFB6C1',
            fg='white',
            width=15,
            height=3,
            command=self.run_basic_version,
            cursor="hand2",
            relief="raised",
            bd=3
        )
        basic_button.pack(pady=10)
        
        # 高级版本按钮
        advanced_button = tk.Button(
            button_frame,
            text="✨ 高级版本 ✨\n动画效果",
            font=("微软雅黑", 14, "bold"),
            bg='#FF69B4',
            fg='white',
            width=15,
            height=3,
            command=self.run_advanced_version,
            cursor="hand2",
            relief="raised",
            bd=3
        )
        advanced_button.pack(pady=10)
        
        # 说明文字
        info_label = tk.Label(
            self.root,
            text="💡 建议：第一次使用选择基础版本\n想要更多特效选择高级版本",
            font=("微软雅黑", 10),
            bg='#FFE4E1',
            fg='#8B008B',
            justify=tk.CENTER
        )
        info_label.pack(side=tk.BOTTOM, pady=20)
        
    def run_basic_version(self):
        """运行基础版本"""
        try:
            if os.path.exists("main.py"):
                subprocess.Popen([sys.executable, "main.py"])
                self.root.quit()
            else:
                messagebox.showerror("错误", "找不到 main.py 文件！")
        except Exception as e:
            messagebox.showerror("错误", f"启动失败：{e}")
            
    def run_advanced_version(self):
        """运行高级版本"""
        try:
            if os.path.exists("advanced_version.py"):
                subprocess.Popen([sys.executable, "advanced_version.py"])
                self.root.quit()
            else:
                messagebox.showerror("错误", "找不到 advanced_version.py 文件！")
        except Exception as e:
            messagebox.showerror("错误", f"启动失败：{e}")

def main():
    """主函数"""
    try:
        app = LauncherApp()
        app.root.mainloop()
    except Exception as e:
        print(f"启动器运行出错: {e}")
        messagebox.showerror("错误", f"启动器运行出错: {e}")

if __name__ == "__main__":
    main()
