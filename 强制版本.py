#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
爱不爱我测试程序 - 强制版本
不选择"爱我"就无法退出的霸道版本
"""

import tkinter as tk
from tkinter import messagebox
import random
import sys
import threading
import time

class ForceloveLoveTestApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("💕 你必须爱我！ 💕")
        self.root.geometry("600x500")
        self.root.configure(bg='#FFB6C1')
        self.root.resizable(False, False)
        
        # 居中显示窗口
        self.center_window()
        
        # 强制置顶
        self.root.attributes('-topmost', True)
        
        # 阻止用户关闭窗口
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 禁用Alt+F4等快捷键
        self.root.bind('<Alt-F4>', lambda e: self.block_exit())
        self.root.bind('<Control-c>', lambda e: self.block_exit())
        self.root.bind('<Control-z>', lambda e: self.block_exit())
        self.root.bind('<Escape>', lambda e: self.block_exit())
        
        # 计数器
        self.rejection_count = 0
        self.can_close = False
        
        # 反问消息列表
        self.rejection_messages = [
            "不可能！你一定是点错了！😤",
            "我不信！你心里一定有我！💖",
            "别闹了，快说爱我！😘",
            "你这样让我很伤心！😭",
            "我知道你在口是心非！😌",
            "不许骗我！你一定爱我！💕",
            "我要生气了！快选爱我！😡",
            "求求你了，选爱我吧！🙏",
            "我的心都要碎了！💔",
            "最后一次机会！必须选爱我！😠"
        ]
        
        self.setup_ui()
        self.start_force_focus()
        
    def center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
    def setup_ui(self):
        """设置用户界面"""
        # 标题
        self.title_label = tk.Label(
            self.root,
            text="💕 你必须爱我！ 💕",
            font=("微软雅黑", 28, "bold"),
            bg='#FFB6C1',
            fg='#FF1493'
        )
        self.title_label.pack(pady=40)
        
        # 副标题
        self.subtitle_label = tk.Label(
            self.root,
            text="不选择爱我就不能离开哦～",
            font=("微软雅黑", 16),
            bg='#FFB6C1',
            fg='#FF69B4'
        )
        self.subtitle_label.pack(pady=10)
        
        # 拒绝计数显示
        self.count_label = tk.Label(
            self.root,
            text="",
            font=("微软雅黑", 14, "bold"),
            bg='#FFB6C1',
            fg='#8B008B'
        )
        self.count_label.pack(pady=10)
        
        # 按钮框架
        self.button_frame = tk.Frame(self.root, bg='#FFB6C1')
        self.button_frame.pack(pady=50)
        
        # "爱我"按钮 - 固定位置
        self.love_button = tk.Button(
            self.button_frame,
            text="💖 爱我 💖",
            font=("微软雅黑", 20, "bold"),
            bg='#FF69B4',
            fg='white',
            width=12,
            height=2,
            command=self.love_clicked,
            cursor="hand2",
            relief="raised",
            bd=5
        )
        self.love_button.pack(side=tk.LEFT, padx=30)
        
        # "不爱我"按钮 - 会移动位置
        self.no_love_button = tk.Button(
            self.button_frame,
            text="💔 不爱 💔",
            font=("微软雅黑", 20, "bold"),
            bg='#DC143C',
            fg='white',
            width=12,
            height=2,
            command=self.no_love_clicked,
            cursor="hand2",
            relief="raised",
            bd=5
        )
        self.no_love_button.pack(side=tk.RIGHT, padx=30)
        
        # 警告文字
        self.warning_label = tk.Label(
            self.root,
            text="⚠️ 警告：不选择爱我就无法关闭程序！",
            font=("微软雅黑", 12, "bold"),
            bg='#FFB6C1',
            fg='#DC143C'
        )
        self.warning_label.pack(side=tk.BOTTOM, pady=20)
        
    def start_force_focus(self):
        """强制保持窗口焦点"""
        def force_focus():
            while not self.can_close:
                try:
                    self.root.lift()
                    self.root.focus_force()
                    self.root.attributes('-topmost', True)
                    time.sleep(1)
                except:
                    break
                    
        thread = threading.Thread(target=force_focus, daemon=True)
        thread.start()
        
    def love_clicked(self):
        """点击爱我按钮的处理"""
        success_messages = [
            "太好了！我就知道你爱我！💕\n现在可以关闭程序了～",
            "我也爱你！💖\n谢谢你的诚实回答！",
            "我们永远在一起！💑\n程序即将关闭～",
            "你是我的唯一！💝\n爱你哦～"
        ]
        
        message = random.choice(success_messages)
        messagebox.showinfo("💕 太棒了！", message)
        
        self.can_close = True
        self.root.attributes('-topmost', False)
        
        # 显示最终界面
        self.show_final_screen()
        
    def show_final_screen(self):
        """显示最终的成功界面"""
        # 清空当前界面
        for widget in self.root.winfo_children():
            widget.destroy()
            
        # 创建成功界面
        self.root.configure(bg='#FF1493')
        
        final_label = tk.Label(
            self.root,
            text="💖💖💖\n我爱你\n💖💖💖\n\n程序将在3秒后关闭",
            font=("微软雅黑", 32, "bold"),
            bg='#FF1493',
            fg='white',
            justify=tk.CENTER
        )
        final_label.pack(expand=True)
        
        # 3秒后关闭程序
        self.root.after(3000, self.safe_quit)
        
    def safe_quit(self):
        """安全退出程序"""
        self.can_close = True
        self.root.quit()
        self.root.destroy()
        
    def no_love_clicked(self):
        """点击不爱我按钮的处理"""
        self.rejection_count += 1
        
        # 更新计数显示
        self.count_label.config(
            text=f"你已经拒绝了我 {self.rejection_count} 次！😢\n程序仍然无法关闭！"
        )
        
        # 随机选择反问消息
        message = random.choice(self.rejection_messages)
        
        # 根据拒绝次数改变消息框类型
        if self.rejection_count <= 3:
            messagebox.showwarning("😢 不可能！", message)
        elif self.rejection_count <= 6:
            messagebox.showerror("😭 我的心碎了！", message)
        else:
            messagebox.showerror("😡 我生气了！", f"{message}\n\n你已经拒绝了我{self.rejection_count}次！")
        
        # 移动"不爱我"按钮到随机位置
        self.move_no_love_button()
        
        # 让按钮变得更小更难点击
        self.make_button_harder()
        
        # 改变界面颜色表示"愤怒"
        if self.rejection_count > 5:
            self.root.configure(bg='#FF6347')  # 番茄红色
            self.button_frame.configure(bg='#FF6347')
            self.title_label.configure(bg='#FF6347')
            self.subtitle_label.configure(bg='#FF6347')
            self.count_label.configure(bg='#FF6347')
            self.warning_label.configure(bg='#FF6347')
            
        # 更新标题显示愤怒
        if self.rejection_count > 8:
            self.title_label.config(text="😡 你必须爱我！😡")
            self.subtitle_label.config(text="不许再拒绝我了！！！")
            
    def make_button_harder(self):
        """让不爱我按钮变得更难点击"""
        # 按钮变小
        width = max(6, 12 - self.rejection_count)
        height = max(1, 2 - self.rejection_count // 5)
        self.no_love_button.config(width=width, height=height)
        
        # 字体变小
        font_size = max(8, 20 - self.rejection_count)
        self.no_love_button.config(font=("微软雅黑", font_size, "bold"))
        
    def move_no_love_button(self):
        """随机移动不爱我按钮的位置"""
        # 获取窗口尺寸
        window_width = self.root.winfo_width()
        window_height = self.root.winfo_height()
        
        # 按钮尺寸（会根据拒绝次数变化）
        button_width = max(60, 120 - self.rejection_count * 5)
        button_height = max(25, 50 - self.rejection_count * 2)
        
        # 随机位置（确保按钮完全在窗口内）
        x = random.randint(10, window_width - button_width - 10)
        y = random.randint(100, window_height - button_height - 50)
        
        # 移动按钮
        self.no_love_button.place(x=x, y=y)
        
    def block_exit(self):
        """阻止退出操作"""
        messagebox.showerror("🚫 不许逃跑！", "必须选择爱我才能关闭程序！😤")
        return "break"
        
    def on_closing(self):
        """处理窗口关闭事件"""
        if not self.can_close:
            messages = [
                "😤 不许关闭！必须选择爱我！",
                "🚫 想逃跑？没门！",
                "😠 不回答问题就不能走！",
                "💔 你这样让我很伤心...",
                "😡 我生气了！快选择爱我！"
            ]
            message = random.choice(messages)
            messagebox.showerror("不许关闭！", message)
        else:
            self.root.destroy()

def main():
    """主函数"""
    try:
        app = ForceloveLoveTestApp()
        app.root.mainloop()
    except KeyboardInterrupt:
        print("\n程序被强制中断")
    except Exception as e:
        print(f"程序运行出错: {e}")

if __name__ == "__main__":
    main()
