#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
爱不爱我测试程序 - Flask Web版本
可以在手机浏览器中使用
"""

from flask import Flask, render_template, request, jsonify, session
import random
import os

app = Flask(__name__)
app.secret_key = 'love_test_secret_key_2024'

# 反问消息列表
GENTLE_MESSAGES = [
    "真的不爱我吗？😢",
    "再想想嘛～ 💭",
    "你确定吗？我会很伤心的 😭"
]

MEDIUM_MESSAGES = [
    "不可能！你一定是点错了 😤",
    "我不信！再选一次 😠",
    "你的心里一定有我的 💖"
]

INTENSE_MESSAGES = [
    "别闹了，快说爱我 😘",
    "我这么可爱，你怎么能不爱我 🥺",
    "最后一次机会哦 😏",
    "我知道你在口是心非 😌"
]

DESPERATE_MESSAGES = [
    "我要哭了！😭😭😭",
    "你再不选爱我，我就要生气了！😡",
    "我的心都要碎了！💔💔💔",
    "求求你了，选爱我吧！🙏"
]

SUCCESS_MESSAGES = [
    "我就知道你爱我！💕\n我们永远在一起！",
    "太好了！我也爱你！💖\n你是我的唯一！",
    "我们的爱情天长地久！💑\n比钻石还要永恒！",
    "你是我的小甜心！💝\n我要给你全世界的爱！"
]

@app.route('/')
def index():
    """主页面"""
    # 初始化session
    if 'rejection_count' not in session:
        session['rejection_count'] = 0
    return render_template('love_test.html')

@app.route('/love', methods=['POST'])
def love_clicked():
    """处理点击爱我按钮"""
    message = random.choice(SUCCESS_MESSAGES)
    return jsonify({
        'success': True,
        'message': message,
        'type': 'love'
    })

@app.route('/no_love', methods=['POST'])
def no_love_clicked():
    """处理点击不爱我按钮"""
    session['rejection_count'] = session.get('rejection_count', 0) + 1
    count = session['rejection_count']
    
    # 根据拒绝次数选择消息
    if count <= 3:
        message = random.choice(GENTLE_MESSAGES)
        level = 'gentle'
    elif count <= 6:
        message = random.choice(MEDIUM_MESSAGES)
        level = 'medium'
    elif count <= 9:
        message = random.choice(INTENSE_MESSAGES)
        level = 'intense'
    else:
        message = random.choice(DESPERATE_MESSAGES)
        level = 'desperate'
    
    return jsonify({
        'success': False,
        'message': message,
        'rejection_count': count,
        'level': level,
        'type': 'rejection'
    })

@app.route('/reset', methods=['POST'])
def reset_session():
    """重置session"""
    session['rejection_count'] = 0
    return jsonify({'success': True})

if __name__ == '__main__':
    # 创建templates目录
    if not os.path.exists('templates'):
        os.makedirs('templates')
    
    # 创建HTML模板
    create_template()
    
    print("🚀 Flask服务器启动中...")
    print("📱 在手机浏览器中访问: http://你的电脑IP:5000")
    print("💻 在电脑浏览器中访问: http://localhost:5000")
    print("⚠️  确保手机和电脑在同一WiFi网络下")
    
    app.run(host='0.0.0.0', port=5000, debug=True)

def create_template():
    """创建HTML模板文件"""
    template_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💕 你爱我吗？ 💕</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #FFB6C1, #FFC0CB, #FFE4E1);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            overflow-x: hidden;
            position: relative;
        }

        .container {
            text-align: center;
            padding: 20px;
            max-width: 90%;
            width: 400px;
        }

        .title {
            font-size: 2.5em;
            color: #FF1493;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
            animation: heartbeat 2s ease-in-out infinite;
        }

        .subtitle {
            font-size: 1.2em;
            color: #FF69B4;
            margin-bottom: 30px;
        }

        .button-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin: 40px 0;
            position: relative;
            min-height: 200px;
        }

        .love-button {
            background: linear-gradient(45deg, #FF69B4, #FF1493);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.3em;
            font-weight: bold;
            border-radius: 25px;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(255, 20, 147, 0.3);
            transition: all 0.3s ease;
        }

        .love-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 20, 147, 0.4);
        }

        .no-love-button {
            background: linear-gradient(45deg, #DC143C, #B22222);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.3em;
            font-weight: bold;
            border-radius: 25px;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(220, 20, 60, 0.3);
            transition: all 0.3s ease;
            position: absolute;
            top: 80px;
            left: 50%;
            transform: translateX(-50%);
        }

        .rejection-count {
            color: #8B008B;
            font-size: 1.1em;
            margin: 20px 0;
            font-weight: bold;
        }

        .hint {
            color: #8B008B;
            font-size: 0.9em;
            margin-top: 30px;
        }

        @keyframes heartbeat {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(-50%) translateY(0); }
            25% { transform: translateX(-50%) translateY(-5px); }
            75% { transform: translateX(-50%) translateY(5px); }
        }

        .shake {
            animation: shake 0.5s ease-in-out;
        }

        /* 移动端适配 */
        @media (max-width: 480px) {
            .title {
                font-size: 2em;
            }
            
            .subtitle {
                font-size: 1em;
            }
            
            .love-button, .no-love-button {
                font-size: 1.1em;
                padding: 12px 25px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">💕 你爱我吗？ 💕</h1>
        <p class="subtitle">请诚实回答哦～</p>
        
        <div class="rejection-count" id="rejectionCount"></div>
        
        <div class="button-container">
            <button class="love-button" onclick="loveClicked()">💖 爱我 💖</button>
            <button class="no-love-button" id="noLoveButton" onclick="noLoveClicked()">💔 不爱 💔</button>
        </div>
        
        <p class="hint">💡 提示：选择你内心真实的想法</p>
    </div>

    <script>
        function loveClicked() {
            fetch('/love', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                alert(data.message);
                if (confirm('💕 谢谢你的爱！要重新开始吗？')) {
                    resetGame();
                }
            });
        }

        function noLoveClicked() {
            fetch('/no_love', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                alert(data.message);
                
                // 更新拒绝计数
                document.getElementById('rejectionCount').textContent = 
                    `你已经拒绝了我 ${data.rejection_count} 次 😢`;
                
                // 移动按钮
                moveNoLoveButton();
                
                // 根据级别调整按钮
                adjustButton(data.level, data.rejection_count);
                
                // 改变背景颜色
                if (data.rejection_count > 7) {
                    document.body.style.background = 
                        'linear-gradient(135deg, #FF6347, #FF4500, #DC143C)';
                }
            });
        }

        function moveNoLoveButton() {
            const button = document.getElementById('noLoveButton');
            
            // 随机位置
            const maxX = window.innerWidth - 200;
            const maxY = window.innerHeight - 100;
            
            const newX = Math.random() * maxX;
            const newY = Math.random() * maxY;
            
            button.style.position = 'fixed';
            button.style.left = newX + 'px';
            button.style.top = newY + 'px';
            button.style.transform = 'none';
            
            // 添加震动效果
            button.classList.add('shake');
            setTimeout(() => {
                button.classList.remove('shake');
            }, 500);
        }

        function adjustButton(level, count) {
            const button = document.getElementById('noLoveButton');
            
            if (count > 5) {
                // 按钮变小
                const scale = Math.max(0.5, 1 - count * 0.05);
                button.style.transform += ` scale(${scale})`;
            }
        }

        function resetGame() {
            fetch('/reset', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(() => {
                location.reload();
            });
        }

        // 阻止页面关闭
        window.addEventListener('beforeunload', function(e) {
            const count = document.getElementById('rejectionCount').textContent;
            if (count && count.includes('次')) {
                e.preventDefault();
                e.returnValue = '不回答问题就不能走哦！😤';
                return '不回答问题就不能走哦！😤';
            }
        });
    </script>
</body>
</html>'''
    
    with open('templates/love_test.html', 'w', encoding='utf-8') as f:
        f.write(template_content)
