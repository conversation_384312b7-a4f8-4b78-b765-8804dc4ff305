#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
爱不爱我小游戏 - 高级版本
包含音效、动画和更多互动功能
"""

import tkinter as tk
from tkinter import messagebox
import random
import threading
import time
import math

class AdvancedLoveTestApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("💕 爱不爱我测试 - 高级版 💕")
        self.root.geometry("600x500")
        self.root.configure(bg='#FFB6C1')
        self.root.resizable(False, False)
        
        # 居中显示窗口
        self.center_window()
        
        # 阻止用户关闭窗口
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 计数器
        self.rejection_count = 0
        self.max_rejections = 10
        
        # 反问消息列表（按严重程度分级）
        self.gentle_messages = [
            "真的不爱我吗？😢",
            "再想想嘛～ 💭",
            "你确定吗？我会很伤心的 😭"
        ]
        
        self.medium_messages = [
            "不可能！你一定是点错了 😤",
            "我不信！再选一次 😠",
            "你的心里一定有我的 💖"
        ]
        
        self.intense_messages = [
            "别闹了，快说爱我 😘",
            "我这么可爱，你怎么能不爱我 🥺",
            "最后一次机会哦 😏",
            "我知道你在口是心非 😌"
        ]
        
        self.desperate_messages = [
            "我要哭了！😭😭😭",
            "你再不选爱我，我就要生气了！😡",
            "我的心都要碎了！💔💔💔",
            "求求你了，选爱我吧！🙏"
        ]
        
        self.setup_ui()
        
    def center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
    def setup_ui(self):
        """设置用户界面"""
        # 标题
        self.title_label = tk.Label(
            self.root,
            text="💕 你爱我吗？ 💕",
            font=("微软雅黑", 28, "bold"),
            bg='#FFB6C1',
            fg='#FF1493'
        )
        self.title_label.pack(pady=40)
        
        # 副标题
        self.subtitle_label = tk.Label(
            self.root,
            text="请诚实回答哦～",
            font=("微软雅黑", 16),
            bg='#FFB6C1',
            fg='#FF69B4'
        )
        self.subtitle_label.pack(pady=10)
        
        # 拒绝计数显示
        self.count_label = tk.Label(
            self.root,
            text="",
            font=("微软雅黑", 12),
            bg='#FFB6C1',
            fg='#8B008B'
        )
        self.count_label.pack(pady=5)
        
        # 按钮框架
        self.button_frame = tk.Frame(self.root, bg='#FFB6C1')
        self.button_frame.pack(pady=50)
        
        # "爱我"按钮 - 固定位置
        self.love_button = tk.Button(
            self.button_frame,
            text="💖 爱我 💖",
            font=("微软雅黑", 18, "bold"),
            bg='#FF69B4',
            fg='white',
            width=12,
            height=2,
            command=self.love_clicked,
            cursor="hand2",
            relief="raised",
            bd=3
        )
        self.love_button.pack(side=tk.LEFT, padx=30)
        
        # "不爱我"按钮 - 会移动位置
        self.no_love_button = tk.Button(
            self.button_frame,
            text="💔 不爱 💔",
            font=("微软雅黑", 18, "bold"),
            bg='#DC143C',
            fg='white',
            width=12,
            height=2,
            command=self.no_love_clicked,
            cursor="hand2",
            relief="raised",
            bd=3
        )
        self.no_love_button.pack(side=tk.RIGHT, padx=30)
        
        # 底部提示
        self.hint_label = tk.Label(
            self.root,
            text="💡 提示：选择你内心真实的想法",
            font=("微软雅黑", 12),
            bg='#FFB6C1',
            fg='#8B008B'
        )
        self.hint_label.pack(side=tk.BOTTOM, pady=20)
        
        # 开始心跳动画
        self.start_heartbeat_animation()
        
    def start_heartbeat_animation(self):
        """开始心跳动画"""
        def animate():
            scale = 1.0
            direction = 1
            while True:
                try:
                    scale += direction * 0.05
                    if scale >= 1.2:
                        direction = -1
                    elif scale <= 1.0:
                        direction = 1
                    
                    size = int(28 * scale)
                    self.title_label.config(font=("微软雅黑", size, "bold"))
                    time.sleep(0.1)
                except:
                    break
                    
        thread = threading.Thread(target=animate, daemon=True)
        thread.start()
        
    def get_rejection_message(self):
        """根据拒绝次数获取相应的消息"""
        if self.rejection_count <= 3:
            return random.choice(self.gentle_messages)
        elif self.rejection_count <= 6:
            return random.choice(self.medium_messages)
        elif self.rejection_count <= 9:
            return random.choice(self.intense_messages)
        else:
            return random.choice(self.desperate_messages)
            
    def love_clicked(self):
        """点击爱我按钮的处理"""
        success_messages = [
            "我就知道你爱我！💕\n我们永远在一起！",
            "太好了！我也爱你！💖\n你是我的唯一！",
            "我们的爱情天长地久！💑\n比钻石还要永恒！",
            "你是我的小甜心！💝\n我要给你全世界的爱！"
        ]
        
        message = random.choice(success_messages)
        messagebox.showinfo("💕 太棒了！", message)
        
        # 显示最终的爱心界面
        self.show_final_love_screen()
        
    def show_final_love_screen(self):
        """显示最终的爱心界面"""
        # 清空当前界面
        for widget in self.root.winfo_children():
            widget.destroy()
            
        # 创建爱心界面
        self.root.configure(bg='#FF1493')
        
        # 创建爱心图案
        canvas = tk.Canvas(self.root, width=600, height=400, bg='#FF1493', highlightthickness=0)
        canvas.pack(expand=True)
        
        # 绘制多个爱心
        self.draw_hearts(canvas)
        
        final_label = tk.Label(
            self.root,
            text="💖 我爱你 💖\n💕 Forever 💕",
            font=("微软雅黑", 36, "bold"),
            bg='#FF1493',
            fg='white'
        )
        final_label.place(relx=0.5, rely=0.5, anchor='center')
        
        # 5秒后关闭程序
        self.root.after(5000, self.root.quit)
        
    def draw_hearts(self, canvas):
        """在画布上绘制爱心"""
        for i in range(20):
            x = random.randint(50, 550)
            y = random.randint(50, 350)
            size = random.randint(10, 30)
            self.draw_heart(canvas, x, y, size)
            
    def draw_heart(self, canvas, x, y, size):
        """绘制单个爱心"""
        # 简化的爱心形状
        points = []
        for i in range(360):
            angle = math.radians(i)
            heart_x = size * (16 * math.sin(angle)**3)
            heart_y = size * (13 * math.cos(angle) - 5 * math.cos(2*angle) - 2 * math.cos(3*angle) - math.cos(4*angle))
            points.extend([x + heart_x/16, y - heart_y/16])
            
        if len(points) > 6:
            canvas.create_polygon(points, fill='white', outline='pink', width=2)
        
    def no_love_clicked(self):
        """点击不爱我按钮的处理"""
        self.rejection_count += 1
        
        # 更新计数显示
        self.count_label.config(text=f"你已经拒绝了我 {self.rejection_count} 次 😢")
        
        # 获取相应级别的消息
        message = self.get_rejection_message()
        
        # 根据拒绝次数改变消息框类型
        if self.rejection_count <= 5:
            messagebox.showwarning("😢 不可能！", message)
        else:
            messagebox.showerror("😭 我的心碎了！", message)
        
        # 移动"不爱我"按钮到随机位置
        self.move_no_love_button()
        
        # 如果拒绝次数过多，让按钮变得更小更难点击
        if self.rejection_count > 5:
            self.make_button_harder()
            
        # 改变界面颜色表示"愤怒"
        if self.rejection_count > 7:
            self.root.configure(bg='#FF6347')  # 番茄红色
            
    def make_button_harder(self):
        """让不爱我按钮变得更难点击"""
        # 按钮变小
        width = max(8, 12 - self.rejection_count)
        height = max(1, 2 - self.rejection_count // 5)
        self.no_love_button.config(width=width, height=height)
        
        # 字体变小
        font_size = max(10, 18 - self.rejection_count)
        self.no_love_button.config(font=("微软雅黑", font_size, "bold"))
        
    def move_no_love_button(self):
        """随机移动不爱我按钮的位置"""
        # 获取窗口尺寸
        window_width = self.root.winfo_width()
        window_height = self.root.winfo_height()
        
        # 按钮尺寸（会根据拒绝次数变化）
        button_width = max(80, 120 - self.rejection_count * 5)
        button_height = max(30, 50 - self.rejection_count * 2)
        
        # 随机位置（确保按钮完全在窗口内）
        x = random.randint(10, window_width - button_width - 10)
        y = random.randint(100, window_height - button_height - 50)
        
        # 移动按钮
        self.no_love_button.place(x=x, y=y)
        
        # 添加移动动画效果
        self.animate_button_move(x, y)
        
    def animate_button_move(self, target_x, target_y):
        """按钮移动动画"""
        current_x = self.no_love_button.winfo_x()
        current_y = self.no_love_button.winfo_y()
        
        steps = 10
        dx = (target_x - current_x) / steps
        dy = (target_y - current_y) / steps
        
        def move_step(step):
            if step < steps:
                new_x = current_x + dx * step
                new_y = current_y + dy * step
                self.no_love_button.place(x=new_x, y=new_y)
                self.root.after(20, lambda: move_step(step + 1))
                
        move_step(0)
        
    def on_closing(self):
        """处理窗口关闭事件"""
        messages = [
            "😤 不许关闭！不回答问题就不能走！",
            "🚫 想逃跑？没门！",
            "😠 必须选择一个答案才能关闭！",
            "💔 你这样让我很伤心..."
        ]
        message = random.choice(messages)
        messagebox.showwarning("不许关闭！", message)

def main():
    """主函数"""
    try:
        app = AdvancedLoveTestApp()
        app.root.mainloop()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
        messagebox.showerror("错误", f"程序运行出错: {e}")

if __name__ == "__main__":
    main()
