@echo off
chcp 65001 >nul
title 💕 爱不爱我测试程序 💕

echo.
echo ================================
echo    💕 爱不爱我测试程序 💕
echo ================================
echo.
echo 1. 启动程序选择器
echo 2. 直接运行基础版本
echo 3. 直接运行高级版本
echo 4. 运行强制版本（桌面）
echo 5. 打开强制版本（网页）
echo.
set /p choice="请选择 (1/2/3/4/5): "

if "%choice%"=="1" (
    echo 正在启动程序选择器...
    python 启动程序.py
) else if "%choice%"=="2" (
    echo 正在启动基础版本...
    python main.py
) else if "%choice%"=="3" (
    echo 正在启动高级版本...
    python advanced_version.py
) else if "%choice%"=="4" (
    echo 正在启动强制版本（桌面）...
    echo ⚠️ 警告：这个版本不选择爱我就无法关闭！
    timeout /t 3 /nobreak >nul
    python 强制版本.py
) else if "%choice%"=="5" (
    echo 正在打开强制版本（网页）...
    echo ⚠️ 警告：这个版本不选择爱我就无法关闭！
    timeout /t 3 /nobreak >nul
    start 超级强制版.html
) else (
    echo 默认启动程序选择器...
    python 启动程序.py
)

if %errorlevel% neq 0 (
    echo.
    echo ❌ 程序运行出错！
    echo 请确保已安装Python 3.6或更高版本
    echo.
    pause
) else (
    echo.
    echo ✅ 程序运行完成！
    echo.
)

pause
