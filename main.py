#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
爱不爱我小游戏
一个有趣的GUI程序，测试女朋友是否爱你 💕
"""

import tkinter as tk
from tkinter import messagebox
import random
import sys

class LoveTestApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("💕 爱不爱我测试 💕")
        self.root.geometry("500x400")
        self.root.configure(bg='#FFB6C1')  # 浅粉色背景
        self.root.resizable(False, False)
        
        # 居中显示窗口
        self.center_window()
        
        # 阻止用户关闭窗口
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 反问消息列表
        self.rejection_messages = [
            "真的不爱我吗？😢",
            "再想想嘛～ 💭",
            "你确定吗？我会很伤心的 😭",
            "不可能！你一定是点错了 😤",
            "我不信！再选一次 😠",
            "你的心里一定有我的 💖",
            "别闹了，快说爱我 😘",
            "我这么可爱，你怎么能不爱我 🥺",
            "最后一次机会哦 😏",
            "我知道你在口是心非 😌"
        ]
        
        self.setup_ui()
        
    def center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
    def setup_ui(self):
        """设置用户界面"""
        # 标题
        title_label = tk.Label(
            self.root,
            text="💕 你爱我吗？ 💕",
            font=("微软雅黑", 24, "bold"),
            bg='#FFB6C1',
            fg='#FF1493'
        )
        title_label.pack(pady=50)
        
        # 副标题
        subtitle_label = tk.Label(
            self.root,
            text="请诚实回答哦～",
            font=("微软雅黑", 14),
            bg='#FFB6C1',
            fg='#FF69B4'
        )
        subtitle_label.pack(pady=10)
        
        # 按钮框架
        button_frame = tk.Frame(self.root, bg='#FFB6C1')
        button_frame.pack(pady=50)
        
        # "爱我"按钮 - 固定位置
        self.love_button = tk.Button(
            button_frame,
            text="💖 爱我 💖",
            font=("微软雅黑", 16, "bold"),
            bg='#FF69B4',
            fg='white',
            width=12,
            height=2,
            command=self.love_clicked,
            cursor="hand2"
        )
        self.love_button.pack(side=tk.LEFT, padx=20)
        
        # "不爱我"按钮 - 会移动位置
        self.no_love_button = tk.Button(
            button_frame,
            text="💔 不爱 💔",
            font=("微软雅黑", 16, "bold"),
            bg='#DC143C',
            fg='white',
            width=12,
            height=2,
            command=self.no_love_clicked,
            cursor="hand2"
        )
        self.no_love_button.pack(side=tk.RIGHT, padx=20)
        
        # 底部提示
        hint_label = tk.Label(
            self.root,
            text="💡 提示：选择你内心真实的想法",
            font=("微软雅黑", 10),
            bg='#FFB6C1',
            fg='#8B008B'
        )
        hint_label.pack(side=tk.BOTTOM, pady=20)
        
    def love_clicked(self):
        """点击爱我按钮的处理"""
        success_messages = [
            "我就知道你爱我！💕",
            "太好了！我也爱你！💖",
            "我们永远在一起！💑",
            "你是我的唯一！💝"
        ]
        
        message = random.choice(success_messages)
        messagebox.showinfo("💕 太棒了！", message)
        
        # 显示最终的爱心界面
        self.show_final_love_screen()
        
    def show_final_love_screen(self):
        """显示最终的爱心界面"""
        # 清空当前界面
        for widget in self.root.winfo_children():
            widget.destroy()
            
        # 创建爱心界面
        self.root.configure(bg='#FF1493')
        
        final_label = tk.Label(
            self.root,
            text="💖💖💖\n我爱你\n💖💖💖",
            font=("微软雅黑", 32, "bold"),
            bg='#FF1493',
            fg='white'
        )
        final_label.pack(expand=True)
        
        # 3秒后关闭程序
        self.root.after(3000, self.root.quit)
        
    def no_love_clicked(self):
        """点击不爱我按钮的处理"""
        # 随机选择一个反问消息
        message = random.choice(self.rejection_messages)
        messagebox.showwarning("😢 不可能！", message)
        
        # 移动"不爱我"按钮到随机位置
        self.move_no_love_button()
        
    def move_no_love_button(self):
        """随机移动不爱我按钮的位置"""
        # 获取窗口尺寸
        window_width = self.root.winfo_width()
        window_height = self.root.winfo_height()
        
        # 按钮尺寸
        button_width = 120
        button_height = 50
        
        # 随机位置（确保按钮完全在窗口内）
        x = random.randint(10, window_width - button_width - 10)
        y = random.randint(100, window_height - button_height - 50)
        
        # 移动按钮
        self.no_love_button.place(x=x, y=y)
        
    def on_closing(self):
        """处理窗口关闭事件"""
        messagebox.showwarning("😤 不许关闭！", "不回答问题就不能关闭哦！")

def main():
    """主函数"""
    try:
        app = LoveTestApp()
        app.root.mainloop()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
        messagebox.showerror("错误", f"程序运行出错: {e}")

if __name__ == "__main__":
    main()
