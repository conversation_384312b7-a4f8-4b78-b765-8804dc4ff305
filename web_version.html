<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💕 你爱我吗？ 💕</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #FFB6C1, #FFC0CB, #FFE4E1);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            overflow-x: hidden;
            position: relative;
        }

        .container {
            text-align: center;
            padding: 20px;
            max-width: 90%;
            width: 400px;
        }

        .title {
            font-size: 2.5em;
            color: #FF1493;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
            animation: heartbeat 2s ease-in-out infinite;
        }

        .subtitle {
            font-size: 1.2em;
            color: #FF69B4;
            margin-bottom: 30px;
        }

        .button-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin: 40px 0;
        }

        .love-button {
            background: linear-gradient(45deg, #FF69B4, #FF1493);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.3em;
            font-weight: bold;
            border-radius: 25px;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(255, 20, 147, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .love-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 20, 147, 0.4);
        }

        .no-love-button {
            background: linear-gradient(45deg, #DC143C, #B22222);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.3em;
            font-weight: bold;
            border-radius: 25px;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(220, 20, 60, 0.3);
            transition: all 0.3s ease;
            position: absolute;
            z-index: 10;
        }

        .no-love-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(220, 20, 60, 0.4);
        }

        .rejection-count {
            color: #8B008B;
            font-size: 1.1em;
            margin: 20px 0;
            font-weight: bold;
        }

        .hint {
            color: #8B008B;
            font-size: 0.9em;
            margin-top: 30px;
        }

        .hearts {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .heart {
            position: absolute;
            color: rgba(255, 182, 193, 0.6);
            font-size: 20px;
            animation: float 6s ease-in-out infinite;
        }

        .final-screen {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, #FF1493, #FF69B4);
            z-index: 1000;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }

        .final-message {
            color: white;
            font-size: 3em;
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            animation: pulse 1.5s ease-in-out infinite;
        }

        @keyframes heartbeat {

            0%,
            100% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.1);
            }
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0;
            }

            50% {
                transform: translateY(-20px) rotate(180deg);
                opacity: 1;
            }
        }

        @keyframes pulse {

            0%,
            100% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.05);
            }
        }

        @keyframes shake {

            0%,
            100% {
                transform: translateX(0);
            }

            25% {
                transform: translateX(-5px);
            }

            75% {
                transform: translateX(5px);
            }
        }

        .shake {
            animation: shake 0.5s ease-in-out;
        }

        /* 移动端适配 */
        @media (max-width: 480px) {
            .title {
                font-size: 2em;
            }

            .subtitle {
                font-size: 1em;
            }

            .love-button,
            .no-love-button {
                font-size: 1.1em;
                padding: 12px 25px;
            }

            .final-message {
                font-size: 2em;
            }
        }
    </style>
</head>

<body>
    <div class="hearts" id="hearts"></div>

    <div class="container">
        <h1 class="title">💕 你爱我吗？ 💕</h1>
        <p class="subtitle">请诚实回答哦～</p>

        <div class="rejection-count" id="rejectionCount"></div>

        <div class="button-container">
            <button class="love-button" onclick="loveClicked()">💖 爱我 💖</button>
            <button class="no-love-button" id="noLoveButton" onclick="noLoveClicked()">💔 不爱 💔</button>
        </div>

        <p class="hint">💡 提示：选择你内心真实的想法</p>
    </div>

    <div class="final-screen" id="finalScreen">
        <div class="final-message">
            💖 我爱你 💖<br>
            💕 Forever 💕
        </div>
    </div>

    <script>
        let rejectionCount = 0;
        const maxRejections = 10;

        const gentleMessages = [
            "真的不爱我吗？😢",
            "再想想嘛～ 💭",
            "你确定吗？我会很伤心的 😭"
        ];

        const mediumMessages = [
            "不可能！你一定是点错了 😤",
            "我不信！再选一次 😠",
            "你的心里一定有我的 💖"
        ];

        const intenseMessages = [
            "别闹了，快说爱我 😘",
            "我这么可爱，你怎么能不爱我 🥺",
            "最后一次机会哦 😏",
            "我知道你在口是心非 😌"
        ];

        const desperateMessages = [
            "我要哭了！😭😭😭",
            "你再不选爱我，我就要生气了！😡",
            "我的心都要碎了！💔💔💔",
            "求求你了，选爱我吧！🙏"
        ];

        function createHearts() {
            const heartsContainer = document.getElementById('hearts');
            for (let i = 0; i < 20; i++) {
                const heart = document.createElement('div');
                heart.className = 'heart';
                heart.innerHTML = '💖';
                heart.style.left = Math.random() * 100 + '%';
                heart.style.top = Math.random() * 100 + '%';
                heart.style.animationDelay = Math.random() * 6 + 's';
                heartsContainer.appendChild(heart);
            }
        }

        function getRejectionMessage() {
            if (rejectionCount <= 3) {
                return gentleMessages[Math.floor(Math.random() * gentleMessages.length)];
            } else if (rejectionCount <= 6) {
                return mediumMessages[Math.floor(Math.random() * mediumMessages.length)];
            } else if (rejectionCount <= 9) {
                return intenseMessages[Math.floor(Math.random() * intenseMessages.length)];
            } else {
                return desperateMessages[Math.floor(Math.random() * desperateMessages.length)];
            }
        }

        function loveClicked() {
            const successMessages = [
                "我就知道你爱我！💕\n我们永远在一起！",
                "太好了！我也爱你！💖\n你是我的唯一！",
                "我们的爱情天长地久！💑\n比钻石还要永恒！",
                "你是我的小甜心！💝\n我要给你全世界的爱！"
            ];

            const message = successMessages[Math.floor(Math.random() * successMessages.length)];
            alert(message);

            showFinalScreen();
        }

        function showFinalScreen() {
            document.getElementById('finalScreen').style.display = 'flex';
            canClose = true; // 允许关闭

            // 3秒后可以关闭
            setTimeout(() => {
                if (confirm('💕 谢谢你的爱！现在可以关闭页面了～')) {
                    window.close();
                }
            }, 3000);
        }

        function noLoveClicked() {
            rejectionCount++;

            // 更新拒绝计数
            document.getElementById('rejectionCount').textContent =
                `你已经拒绝了我 ${rejectionCount} 次 😢`;

            // 显示反问消息
            const message = getRejectionMessage();
            alert(message);

            // 移动按钮
            moveNoLoveButton();

            // 让按钮变得更难点击
            if (rejectionCount > 5) {
                makeButtonHarder();
            }

            // 改变背景颜色表示"愤怒"
            if (rejectionCount > 7) {
                document.body.style.background =
                    'linear-gradient(135deg, #FF6347, #FF4500, #DC143C)';
            }
        }

        function moveNoLoveButton() {
            const button = document.getElementById('noLoveButton');
            const container = document.querySelector('.container');

            // 获取容器尺寸
            const containerRect = container.getBoundingClientRect();
            const buttonRect = button.getBoundingClientRect();

            // 计算随机位置
            const maxX = window.innerWidth - buttonRect.width - 20;
            const maxY = window.innerHeight - buttonRect.height - 20;

            const newX = Math.random() * maxX;
            const newY = Math.random() * maxY;

            // 移动按钮
            button.style.position = 'fixed';
            button.style.left = newX + 'px';
            button.style.top = newY + 'px';

            // 添加震动效果
            button.classList.add('shake');
            setTimeout(() => {
                button.classList.remove('shake');
            }, 500);
        }

        function makeButtonHarder() {
            const button = document.getElementById('noLoveButton');

            // 按钮变小
            const newSize = Math.max(0.6, 1 - rejectionCount * 0.05);
            button.style.transform = `scale(${newSize})`;

            // 字体变小
            const newFontSize = Math.max(0.8, 1.3 - rejectionCount * 0.05);
            button.style.fontSize = newFontSize + 'em';
        }

        // 强制阻止页面关闭
        let canClose = false;

        window.addEventListener('beforeunload', function (e) {
            if (!canClose) {
                e.preventDefault();
                e.returnValue = '不选择爱我就不能离开哦！😤';
                return '不选择爱我就不能离开哦！😤';
            }
        });

        // 阻止返回键
        window.addEventListener('popstate', function (e) {
            if (!canClose) {
                history.pushState(null, null, location.href);
                alert('不许后退！必须选择爱我！😤');
            }
        });

        // 阻止刷新
        window.addEventListener('keydown', function (e) {
            if (!canClose) {
                // 阻止F5刷新
                if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
                    e.preventDefault();
                    alert('不许刷新！必须选择爱我！😤');
                }
                // 阻止Ctrl+W关闭
                if (e.ctrlKey && e.key === 'w') {
                    e.preventDefault();
                    alert('不许关闭！必须选择爱我！😤');
                }
                // 阻止Alt+F4
                if (e.altKey && e.key === 'F4') {
                    e.preventDefault();
                    alert('不许关闭！必须选择爱我！😤');
                }
                // 阻止Esc键
                if (e.key === 'Escape') {
                    e.preventDefault();
                    alert('不许逃跑！必须选择爱我！😤');
                }
            }
        });

        // 初始化时就推入历史记录，防止返回
        history.pushState(null, null, location.href);

        // 初始化
        window.onload = function () {
            createHearts();
        };
    </script>
</body>

</html>