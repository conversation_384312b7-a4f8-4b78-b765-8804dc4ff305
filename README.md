# 💕 爱不爱我测试程序

一个有趣的Python GUI小程序，用来测试女朋友是否爱你！😄

## 🎯 功能特色

- 💖 可爱的粉色界面设计
- 🎮 两个选择按钮："爱我" 和 "不爱我"
- 😈 点击"不爱我"时按钮会随机移动位置
- 💬 多种有趣的反问消息
- 🚫 阻止用户直接关闭程序
- ✨ 只有选择"爱我"才能正常关闭

## 🚀 运行方法

### 方法一：直接运行Python脚本
```bash
python main.py
```

### 方法二：双击批处理文件（Windows）
直接双击 `run.bat` 文件即可运行

## 📋 系统要求

- Python 3.6 或更高版本
- Windows/macOS/Linux 系统
- 支持tkinter（Python内置库）

## 🎮 使用说明

1. 运行程序后会出现一个粉色的窗口
2. 窗口中有两个按钮："💖 爱我 💖" 和 "💔 不爱 💔"
3. 如果点击"不爱"按钮：
   - 会弹出反问对话框
   - 按钮会移动到随机位置
   - 程序不会关闭
4. 如果点击"爱我"按钮：
   - 会显示甜蜜的消息
   - 程序会正常关闭

## 🎨 界面预览

程序界面包含：
- 💕 粉色主题背景
- 🎯 居中的标题文字
- 🔘 两个可爱的按钮
- 💡 底部提示信息

## 📝 自定义修改

你可以在 `main.py` 中修改：
- `rejection_messages` 列表：修改反问消息内容
- 界面颜色：修改 `bg` 和 `fg` 参数
- 窗口大小：修改 `geometry` 参数
- 按钮文字：修改按钮的 `text` 参数

## 🎁 打包成exe文件（可选）

如果想要打包成独立的exe文件，可以使用pyinstaller：

```bash
# 安装pyinstaller
pip install pyinstaller

# 打包成单个exe文件
pyinstaller --onefile --windowed --icon=heart.ico main.py
```

## ⚠️ 注意事项

- 这只是一个娱乐程序，请理性使用
- 程序会阻止直接关闭，只有选择"爱我"才能正常退出
- 如果程序卡死，可以通过任务管理器强制结束

## 💝 使用场景

- 情侣间的小游戏
- 表白时的有趣互动
- 增进感情的小工具
- 编程学习的练习项目

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

---

💕 愿天下有情人终成眷属！ 💕
