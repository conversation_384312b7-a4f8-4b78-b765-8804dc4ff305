<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💕 你必须爱我！ 💕</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            user-select: none; /* 禁止选择文本 */
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #FFB6C1, #FFC0CB, #FFE4E1);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            overflow: hidden; /* 禁止滚动 */
            position: fixed;
            width: 100%;
            height: 100%;
        }

        .container {
            text-align: center;
            padding: 20px;
            max-width: 90%;
            width: 500px;
        }

        .title {
            font-size: 3em;
            color: #FF1493;
            margin-bottom: 20px;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.2);
            animation: heartbeat 1.5s ease-in-out infinite;
        }

        .subtitle {
            font-size: 1.4em;
            color: #FF69B4;
            margin-bottom: 20px;
            font-weight: bold;
        }

        .warning {
            font-size: 1.2em;
            color: #DC143C;
            margin-bottom: 30px;
            font-weight: bold;
            animation: blink 1s infinite;
        }

        .button-container {
            display: flex;
            flex-direction: column;
            gap: 30px;
            margin: 50px 0;
            position: relative;
            min-height: 250px;
        }

        .love-button {
            background: linear-gradient(45deg, #FF69B4, #FF1493);
            color: white;
            border: none;
            padding: 20px 40px;
            font-size: 1.5em;
            font-weight: bold;
            border-radius: 30px;
            cursor: pointer;
            box-shadow: 0 6px 20px rgba(255, 20, 147, 0.4);
            transition: all 0.3s ease;
            position: relative;
            z-index: 100;
        }

        .love-button:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 25px rgba(255, 20, 147, 0.5);
        }

        .no-love-button {
            background: linear-gradient(45deg, #DC143C, #B22222);
            color: white;
            border: none;
            padding: 20px 40px;
            font-size: 1.5em;
            font-weight: bold;
            border-radius: 30px;
            cursor: pointer;
            box-shadow: 0 6px 20px rgba(220, 20, 60, 0.4);
            transition: all 0.3s ease;
            position: absolute;
            top: 120px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 50;
        }

        .rejection-count {
            color: #8B008B;
            font-size: 1.3em;
            margin: 20px 0;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }

        .block-message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(220, 20, 60, 0.95);
            color: white;
            padding: 30px;
            border-radius: 15px;
            font-size: 1.5em;
            font-weight: bold;
            text-align: center;
            z-index: 9999;
            display: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.5);
        }

        @keyframes heartbeat {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(-50%) translateY(0); }
            25% { transform: translateX(-50%) translateY(-10px); }
            75% { transform: translateX(-50%) translateY(10px); }
        }

        @keyframes angry {
            0%, 100% { background: linear-gradient(135deg, #FFB6C1, #FFC0CB, #FFE4E1); }
            50% { background: linear-gradient(135deg, #FF6347, #FF4500, #DC143C); }
        }

        .shake {
            animation: shake 0.5s ease-in-out;
        }

        .angry {
            animation: angry 2s ease-in-out infinite;
        }

        /* 移动端适配 */
        @media (max-width: 480px) {
            .title {
                font-size: 2.2em;
            }
            
            .subtitle {
                font-size: 1.1em;
            }
            
            .warning {
                font-size: 1em;
            }
            
            .love-button, .no-love-button {
                font-size: 1.2em;
                padding: 15px 30px;
            }
        }

        /* 隐藏右键菜单 */
        body {
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title" id="title">💕 你必须爱我！ 💕</h1>
        <p class="subtitle" id="subtitle">不选择爱我就不能离开哦～</p>
        <p class="warning">⚠️ 警告：不选择爱我就无法关闭页面！</p>
        
        <div class="rejection-count" id="rejectionCount"></div>
        
        <div class="button-container">
            <button class="love-button" onclick="loveClicked()">💖 爱我 💖</button>
            <button class="no-love-button" id="noLoveButton" onclick="noLoveClicked()">💔 不爱 💔</button>
        </div>
    </div>

    <div class="block-message" id="blockMessage">
        🚫 不许逃跑！<br>
        必须选择爱我！😤
    </div>

    <script>
        let rejectionCount = 0;
        let canClose = false;
        let blockMessageTimeout;
        
        const rejectionMessages = [
            "不可能！你一定是点错了！😤",
            "我不信！你心里一定有我！💖",
            "别闹了，快说爱我！😘",
            "你这样让我很伤心！😭",
            "我知道你在口是心非！😌",
            "不许骗我！你一定爱我！💕",
            "我要生气了！快选爱我！😡",
            "求求你了，选爱我吧！🙏",
            "我的心都要碎了！💔",
            "最后一次机会！必须选爱我！😠"
        ];

        function showBlockMessage(message = "🚫 不许逃跑！<br>必须选择爱我！😤") {
            const blockMsg = document.getElementById('blockMessage');
            blockMsg.innerHTML = message;
            blockMsg.style.display = 'block';
            
            clearTimeout(blockMessageTimeout);
            blockMessageTimeout = setTimeout(() => {
                blockMsg.style.display = 'none';
            }, 2000);
        }

        function loveClicked() {
            const successMessages = [
                "太好了！我就知道你爱我！💕<br>现在可以关闭页面了～",
                "我也爱你！💖<br>谢谢你的诚实回答！",
                "我们永远在一起！💑<br>页面即将关闭～",
                "你是我的唯一！💝<br>爱你哦～"
            ];
            
            const message = successMessages[Math.floor(Math.random() * successMessages.length)];
            showBlockMessage(message);
            
            canClose = true;
            
            setTimeout(() => {
                showFinalScreen();
            }, 2000);
        }

        function showFinalScreen() {
            document.body.innerHTML = `
                <div style="
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(45deg, #FF1493, #FF69B4);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    flex-direction: column;
                    z-index: 10000;
                ">
                    <div style="
                        color: white;
                        font-size: 3em;
                        text-align: center;
                        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
                        animation: pulse 1.5s ease-in-out infinite;
                    ">
                        💖 我爱你 💖<br>
                        💕 Forever 💕
                    </div>
                    <div style="
                        color: white;
                        font-size: 1.2em;
                        margin-top: 30px;
                        text-align: center;
                    ">
                        页面将在3秒后可以关闭
                    </div>
                </div>
            `;
            
            setTimeout(() => {
                if (confirm('💕 谢谢你的爱！现在可以关闭页面了～')) {
                    window.close();
                }
            }, 3000);
        }

        function noLoveClicked() {
            rejectionCount++;
            
            // 更新拒绝计数
            document.getElementById('rejectionCount').innerHTML = 
                `你已经拒绝了我 ${rejectionCount} 次！😢<br>页面仍然无法关闭！`;
            
            // 显示反问消息
            const message = rejectionMessages[Math.floor(Math.random() * rejectionMessages.length)];
            showBlockMessage(`${message}<br><br>你已经拒绝了我${rejectionCount}次！`);
            
            // 移动按钮
            moveNoLoveButton();
            
            // 让按钮变得更难点击
            makeButtonHarder();
            
            // 改变界面效果
            if (rejectionCount > 3) {
                document.body.classList.add('angry');
            }
            
            if (rejectionCount > 5) {
                document.getElementById('title').textContent = '😡 你必须爱我！😡';
                document.getElementById('subtitle').textContent = '不许再拒绝我了！！！';
            }
        }

        function moveNoLoveButton() {
            const button = document.getElementById('noLoveButton');
            
            // 随机位置
            const maxX = window.innerWidth - 200;
            const maxY = window.innerHeight - 100;
            
            const newX = Math.random() * maxX;
            const newY = Math.random() * maxY;
            
            button.style.position = 'fixed';
            button.style.left = newX + 'px';
            button.style.top = newY + 'px';
            button.style.transform = 'none';
            
            // 添加震动效果
            button.classList.add('shake');
            setTimeout(() => {
                button.classList.remove('shake');
            }, 500);
        }

        function makeButtonHarder() {
            const button = document.getElementById('noLoveButton');
            
            if (rejectionCount > 3) {
                // 按钮变小
                const scale = Math.max(0.4, 1 - rejectionCount * 0.08);
                button.style.transform += ` scale(${scale})`;
                
                // 字体变小
                const fontSize = Math.max(0.8, 1.5 - rejectionCount * 0.08);
                button.style.fontSize = fontSize + 'em';
            }
        }

        // 超级强制阻止所有退出方式
        function blockExit(message = "🚫 不许逃跑！必须选择爱我！😤") {
            if (!canClose) {
                showBlockMessage(message);
                return false;
            }
            return true;
        }

        // 阻止页面关闭
        window.addEventListener('beforeunload', function(e) {
            if (!canClose) {
                e.preventDefault();
                e.returnValue = '不选择爱我就不能离开哦！😤';
                showBlockMessage("🚫 不许关闭！<br>必须选择爱我！😤");
                return '不选择爱我就不能离开哦！😤';
            }
        });

        // 阻止返回键
        window.addEventListener('popstate', function(e) {
            if (!canClose) {
                history.pushState(null, null, location.href);
                showBlockMessage("🚫 不许后退！<br>必须选择爱我！😤");
            }
        });

        // 阻止所有快捷键
        document.addEventListener('keydown', function(e) {
            if (!canClose) {
                // 阻止F5刷新
                if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
                    e.preventDefault();
                    showBlockMessage("🚫 不许刷新！<br>必须选择爱我！😤");
                }
                // 阻止Ctrl+W关闭
                if (e.ctrlKey && e.key === 'w') {
                    e.preventDefault();
                    showBlockMessage("🚫 不许关闭！<br>必须选择爱我！😤");
                }
                // 阻止Alt+F4
                if (e.altKey && e.key === 'F4') {
                    e.preventDefault();
                    showBlockMessage("🚫 不许关闭！<br>必须选择爱我！😤");
                }
                // 阻止Esc键
                if (e.key === 'Escape') {
                    e.preventDefault();
                    showBlockMessage("🚫 不许逃跑！<br>必须选择爱我！😤");
                }
                // 阻止Ctrl+T新标签页
                if (e.ctrlKey && e.key === 't') {
                    e.preventDefault();
                    showBlockMessage("🚫 不许开新页面！<br>必须选择爱我！😤");
                }
                // 阻止Ctrl+N新窗口
                if (e.ctrlKey && e.key === 'n') {
                    e.preventDefault();
                    showBlockMessage("🚫 不许开新窗口！<br>必须选择爱我！😤");
                }
                // 阻止F12开发者工具
                if (e.key === 'F12') {
                    e.preventDefault();
                    showBlockMessage("🚫 不许作弊！<br>必须选择爱我！😤");
                }
                // 阻止Ctrl+Shift+I开发者工具
                if (e.ctrlKey && e.shiftKey && e.key === 'I') {
                    e.preventDefault();
                    showBlockMessage("🚫 不许作弊！<br>必须选择爱我！😤");
                }
            }
        });

        // 阻止右键菜单
        document.addEventListener('contextmenu', function(e) {
            if (!canClose) {
                e.preventDefault();
                showBlockMessage("🚫 不许右键！<br>必须选择爱我！😤");
            }
        });

        // 阻止选择文本
        document.addEventListener('selectstart', function(e) {
            if (!canClose) {
                e.preventDefault();
            }
        });

        // 阻止拖拽
        document.addEventListener('dragstart', function(e) {
            if (!canClose) {
                e.preventDefault();
            }
        });

        // 初始化时就推入历史记录，防止返回
        history.pushState(null, null, location.href);

        // 定期检查焦点，强制保持在当前页面
        setInterval(function() {
            if (!canClose && document.hidden) {
                showBlockMessage("🚫 不许切换页面！<br>必须选择爱我！😤");
            }
        }, 1000);

        // 页面失去焦点时的处理
        window.addEventListener('blur', function() {
            if (!canClose) {
                setTimeout(() => {
                    window.focus();
                }, 100);
            }
        });

        // 阻止页面被嵌入iframe
        if (window.top !== window.self) {
            window.top.location = window.self.location;
        }
    </script>
</body>
</html>
